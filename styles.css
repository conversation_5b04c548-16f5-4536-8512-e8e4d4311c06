/* iOS-Inspired Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Inter', 'Segoe UI', Roboto, sans-serif;
    overflow-x: hidden;
    background: #f2f2f7;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* iOS-style Background */
.ios-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.gradient-mesh {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 122, 255, 0.08) 0%,
        rgba(88, 86, 214, 0.06) 25%,
        rgba(255, 45, 85, 0.04) 50%,
        rgba(52, 199, 89, 0.06) 75%,
        rgba(255, 204, 0, 0.05) 100%
    );
}

.blur-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 20%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 45, 85, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(88, 86, 214, 0.06) 0%, transparent 50%);
    filter: blur(60px);
}

/* iOS Glassmorphism Header */
.ios-header {
    position: fixed;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 48px);
    max-width: 1200px;
    height: 64px;
    z-index: 1000;

    /* iOS Glassmorphism Effect */
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
    background: rgba(255, 255, 255, 0.72);
    border: 0.5px solid rgba(255, 255, 255, 0.18);
    border-radius: 16px;

    /* iOS-style Shadow */
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.04),
        0 8px 16px rgba(0, 0, 0, 0.06),
        0 16px 24px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);

    /* Smooth iOS transitions */
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.ios-header:hover {
    background: rgba(255, 255, 255, 0.8);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.06),
        0 12px 20px rgba(0, 0, 0, 0.08),
        0 20px 32px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    height: 100%;
}

/* iOS Logo Styling */
.logo-container {
    flex-shrink: 0;
}

.logo {
    font-size: 22px;
    font-weight: 600;
    color: #1d1d1f;
    text-decoration: none;
    letter-spacing: -0.4px;
    line-height: 1;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.logo:hover {
    color: #007aff;
    transform: scale(1.02);
}

/* iOS Navigation Styling */
.navigation {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 4px;
    align-items: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 8px 16px;
    color: #1d1d1f;
    text-decoration: none;
    font-weight: 500;
    font-size: 15px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 122, 255, 0.08);
    border-radius: 8px;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-link:hover::before {
    opacity: 1;
    transform: scale(1);
}

.nav-link:hover {
    color: #007aff;
    transform: translateY(-0.5px);
}

.nav-link:active {
    transform: translateY(0) scale(0.98);
}

/* iOS Responsive Design */
@media (max-width: 768px) {
    .ios-header {
        top: 16px;
        width: calc(100% - 32px);
        height: 56px;
    }

    .header-content {
        padding: 0 24px;
    }

    .logo {
        font-size: 20px;
    }

    .nav-list {
        gap: 2px;
    }

    .nav-link {
        padding: 6px 12px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .ios-header {
        top: 12px;
        width: calc(100% - 24px);
        height: 52px;
    }

    .header-content {
        padding: 0 20px;
    }

    .logo {
        font-size: 18px;
    }

    .navigation {
        display: none;
    }
}
